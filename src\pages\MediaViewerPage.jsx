import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';

// Import all the same images as in LifeAtMakonisPage
import img1 from "../assets/1.jpg";
import img3 from "../assets/3.jpg";
import img4 from "../assets/4.jpg";
import img5 from "../assets/5.jpg";
import img6 from "../assets/6.jpg";
import img7 from "../assets/7.jpg";
import img8 from "../assets/8.jpg";
import img9 from "../assets/9.jpg";
import img11 from "../assets/11.jpg";
import img13 from "../assets/13.jpg";
import img14 from "../assets/14.jpg";
import img15 from "../assets/15.jpg";
import img16 from "../assets/16.jpg";
import img18 from "../assets/18.jpg";
import img19 from "../assets/19.jpg";
import img20 from "../assets/20.jpg";
import img21 from "../assets/21.jpg";
import img22 from "../assets/22.jpg";
import img23 from "../assets/23.jpg";
import img25 from "../assets/25.jpg";
import img26 from "../assets/26.jpg";
import img28 from "../assets/28.jpg";
import img29 from "../assets/29.jpg";
import img30 from "../assets/30.jpeg";
import img31 from "../assets/31.jpeg";
import img32 from "../assets/32.jpeg";
import img33 from "../assets/33.png";
import img34 from "../assets/34.jpeg";

// Same array as in LifeAtMakonisPage
const allImages = [
  { id: 1, src: img1 },
  { id: 3, src: img3 },
  { id: 4, src: img4 },
  { id: 5, src: img5 },
  { id: 6, src: img6 },
  { id: 7, src: img7 },
  { id: 8, src: img8 },
  { id: 9, src: img9 },
  { id: 11, src: img11 },
  { id: 13, src: img13 },
  { id: 14, src: img14 },
  { id: 15, src: img15 },
  { id: 16, src: img16 },
  { id: 18, src: img18 },
  { id: 19, src: img19 },
  { id: 20, src: img20 },
  { id: 21, src: img21 },
  { id: 22, src: img22 },
  { id: 23, src: img23 },
  { id: 25, src: img25 },
  { id: 26, src: img26 },
  { id: 28, src: img28 },
  { id: 29, src: img29 },
  { id: 30, src: img30 },
  { id: 31, src: img31 },
  { id: 32, src: img32 },
  { id: 33, src: img33 },
  { id: 34, src: img34 },
];

const MediaViewerPage = () => {
  const { imageId, mode } = useParams();
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isSlideshow, setIsSlideshow] = useState(false);
  const [slideshowInterval, setSlideshowInterval] = useState(null);

  // Find current image index
  useEffect(() => {
    if (imageId) {
      const index = allImages.findIndex(img => img.id.toString() === imageId);
      if (index !== -1) {
        setCurrentIndex(index);
      }
    }
    
    if (mode === 'slideshow') {
      setIsSlideshow(true);
      startSlideshow();
    }
  }, [imageId, mode]);

  const startSlideshow = () => {
    const interval = setInterval(() => {
      setCurrentIndex(prev => (prev + 1) % allImages.length);
    }, 3000);
    setSlideshowInterval(interval);
  };

  const stopSlideshow = () => {
    if (slideshowInterval) {
      clearInterval(slideshowInterval);
      setSlideshowInterval(null);
    }
    setIsSlideshow(false);
  };

  const toggleSlideshow = () => {
    if (isSlideshow) {
      stopSlideshow();
    } else {
      setIsSlideshow(true);
      startSlideshow();
    }
  };

  const goToNext = () => {
    const nextIndex = (currentIndex + 1) % allImages.length;
    setCurrentIndex(nextIndex);
    const nextImageId = allImages[nextIndex].id;
    window.history.replaceState(null, '', `/media-viewer/${nextImageId}/${mode || 'image'}`);
  };

  const goToPrevious = () => {
    const prevIndex = (currentIndex - 1 + allImages.length) % allImages.length;
    setCurrentIndex(prevIndex);
    const prevImageId = allImages[prevIndex].id;
    window.history.replaceState(null, '', `/media-viewer/${prevImageId}/${mode || 'image'}`);
  };

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e) => {
      switch (e.key) {
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
        case ' ':
          e.preventDefault();
          toggleSlideshow();
          break;
        case 'Escape':
          window.close();
          break;
        default:
          break;
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [currentIndex, isSlideshow]);

  useEffect(() => {
    return () => {
      if (slideshowInterval) {
        clearInterval(slideshowInterval);
      }
    };
  }, [slideshowInterval]);

  const currentImage = allImages[currentIndex];

  return (
    <div className="media-viewer">
      <div className="viewer-header">
        <div className="viewer-controls">
          <button onClick={goToPrevious} className="nav-btn">
            ← Previous
          </button>
          <span className="image-counter">
            {currentIndex + 1} of {allImages.length}
          </span>
          <button onClick={goToNext} className="nav-btn">
            Next →
          </button>
        </div>
        <div className="viewer-actions">
          <button onClick={toggleSlideshow} className="slideshow-btn">
            {isSlideshow ? '⏸️ Pause' : '▶️ Slideshow'}
          </button>
          <button onClick={() => window.close()} className="close-btn">
            ✕ Close
          </button>
        </div>
      </div>

      <div className="image-container">
        <button onClick={goToPrevious} className="nav-arrow left-arrow">
          &#8249;
        </button>
        <img 
          src={currentImage.src} 
          alt={`Memory ${currentImage.id}`}
          className="main-image"
        />
        <button onClick={goToNext} className="nav-arrow right-arrow">
          &#8250;
        </button>
      </div>

      <div className="thumbnail-strip">
        {allImages.map((image, index) => (
          <img
            key={image.id}
            src={image.src}
            alt={`Thumbnail ${image.id}`}
            className={`thumbnail ${index === currentIndex ? 'active' : ''}`}
            onClick={() => setCurrentIndex(index)}
          />
        ))}
      </div>

      <div className="shortcuts-info">
        <p>← → Navigate | Space: Slideshow | Esc: Close</p>
      </div>

      <style>{`
        .media-viewer {
          background: #000;
          color: white;
          min-height: 100vh;
          display: flex;
          flex-direction: column;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .viewer-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 1rem 2rem;
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .viewer-controls {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .viewer-actions {
          display: flex;
          align-items: center;
          gap: 1rem;
        }

        .nav-btn, .slideshow-btn, .close-btn {
          padding: 0.5rem 1rem;
          background: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
          color: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 0.9rem;
        }

        .nav-btn:hover, .slideshow-btn:hover, .close-btn:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 157, 230, 0.4);
        }

        .image-counter {
          padding: 0.5rem 1rem;
          background: rgba(255, 255, 255, 0.1);
          border-radius: 8px;
          font-size: 0.9rem;
        }

        .image-container {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 2rem;
          position: relative;
        }

        .main-image {
          max-width: 90%;
          max-height: 70vh;
          object-fit: contain;
          border-radius: 8px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
        }

        .nav-arrow {
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          background: rgba(0, 0, 0, 0.6);
          color: white;
          border: none;
          font-size: 3rem;
          cursor: pointer;
          padding: 1rem;
          transition: all 0.3s ease;
          border-radius: 12px;
          backdrop-filter: blur(10px);
          z-index: 10;
        }

        .nav-arrow:hover {
          background: rgba(0, 157, 230, 0.8);
          transform: translateY(-50%) scale(1.1);
        }

        .left-arrow {
          left: 2rem;
        }

        .right-arrow {
          right: 2rem;
        }

        .thumbnail-strip {
          display: flex;
          gap: 0.5rem;
          padding: 1rem 2rem;
          overflow-x: auto;
          background: rgba(0, 0, 0, 0.8);
          border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .thumbnail {
          width: 60px;
          height: 60px;
          object-fit: cover;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s ease;
          opacity: 0.6;
          flex-shrink: 0;
        }

        .thumbnail:hover {
          opacity: 0.8;
          transform: scale(1.1);
        }

        .thumbnail.active {
          opacity: 1;
          border: 2px solid #009DE6;
          transform: scale(1.1);
        }

        .shortcuts-info {
          text-align: center;
          padding: 0.5rem;
          background: rgba(0, 0, 0, 0.8);
          font-size: 0.8rem;
          opacity: 0.7;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
          .viewer-header {
            flex-direction: column;
            gap: 1rem;
            padding: 1rem;
          }

          .viewer-controls, .viewer-actions {
            gap: 0.5rem;
          }

          .nav-btn, .slideshow-btn, .close-btn {
            padding: 0.4rem 0.8rem;
            font-size: 0.8rem;
          }

          .nav-arrow {
            font-size: 2rem;
            padding: 0.8rem;
          }

          .left-arrow {
            left: 1rem;
          }

          .right-arrow {
            right: 1rem;
          }

          .thumbnail {
            width: 50px;
            height: 50px;
          }

          .thumbnail-strip {
            padding: 0.5rem 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default MediaViewerPage;
