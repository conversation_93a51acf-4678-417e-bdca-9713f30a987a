import React, { useState, useEffect } from "react";

// --- Image Imports ---
// In a real app, you might fetch this list from an API
// For now, ensure the paths are correct relative to this component file
import img1 from "../assets/1.jpg";
import img3 from "../assets/3.jpg";
import img4 from "../assets/4.jpg";
import img5 from "../assets/5.jpg";
import img6 from "../assets/6.jpg";
import img7 from "../assets/7.jpg";
import img8 from "../assets/8.jpg";
import img9 from "../assets/9.jpg";
import img11 from "../assets/11.jpg";
import img13 from "../assets/13.jpg";
import img14 from "../assets/14.jpg";
import img15 from "../assets/15.jpg";
import img16 from "../assets/16.jpg";
import img18 from "../assets/18.jpg";
import img19 from "../assets/19.jpg";
import img20 from "../assets/20.jpg";
import img21 from "../assets/21.jpg";
import img22 from "../assets/22.jpg";
import img23 from "../assets/23.jpg";
import img25 from "../assets/25.jpg";
import img26 from "../assets/26.jpg";
import img28 from "../assets/28.jpg";
import img29 from "../assets/29.jpg";
import img30 from "../assets/30.jpeg";
import img31 from "../assets/31.jpeg";
import img32 from "../assets/32.jpeg";
import img33 from "../assets/33.png";
import img34 from "../assets/34.jpeg";

// Organize images into an array for easier mapping
const allImages = [
  { id: 1, src: img1 },
  { id: 3, src: img3 },
  { id: 4, src: img4 },
  { id: 5, src: img5 },
  { id: 6, src: img6 },
  { id: 7, src: img7 },
  { id: 8, src: img8 },
  { id: 9, src: img9 },
  { id: 11, src: img11 },
  { id: 13, src: img13 },
  { id: 14, src: img14 },
  { id: 15, src: img15 },
  { id: 16, src: img16 },
  { id: 18, src: img18 },
  { id: 19, src: img19 },
  { id: 20, src: img20 },
  { id: 21, src: img21 },
  { id: 22, src: img22 },
  { id: 23, src: img23 },
  { id: 25, src: img25 },
  { id: 26, src: img26 },
  { id: 28, src: img28 },
  { id: 29, src: img29 },
  { id: 30, src: img30 },
  { id: 31, src: img31 },
  { id: 32, src: img32 },
  { id: 33, src: img33 },
  { id: 34, src: img34 },
];

const IMAGES_PER_PAGE = 12;

const GalleryPage = () => {
  // State for the full-screen image modal (now stores index)
  const [selectedImage, setSelectedImage] = useState(null);

  // State for the "Memories" video/slideshow modal
  const [showMemories, setShowMemories] = useState(false);
  const [currentMemoryIndex, setCurrentMemoryIndex] = useState(0);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate paginated images
  const totalPages = Math.ceil(allImages.length / IMAGES_PER_PAGE);
  const paginatedImages = allImages.slice(
    (currentPage - 1) * IMAGES_PER_PAGE,
    currentPage * IMAGES_PER_PAGE
  );

  // Effect for slideshow logic
  useEffect(() => {
    if (!showMemories) return;
    const slideshowInterval = setInterval(() => {
      setCurrentMemoryIndex((prevIndex) => (prevIndex + 1) % allImages.length);
    }, 2000);
    return () => clearInterval(slideshowInterval);
  }, [showMemories]);

  // --- Modal Handlers ---
  const openImageModal = (imageSrc, index) => {
    // We need the global index from `allImages` to navigate
    const globalIndex = allImages.findIndex((img) => img.src === imageSrc);
    setSelectedImage({ src: imageSrc, index: globalIndex });
  };

  const closeImageModal = () => setSelectedImage(null);

  const openMemoriesModal = () => {
    setCurrentMemoryIndex(0);
    setShowMemories(true);
  };

  const closeMemoriesModal = () => setShowMemories(false);

  // --- Navigation Handlers for Full-Screen Modal ---
  const goToNextImage = (e) => {
    e.stopPropagation(); // Prevent modal from closing
    const nextIndex = (selectedImage.index + 1) % allImages.length;
    setSelectedImage({ src: allImages[nextIndex].src, index: nextIndex });
  };

  const goToPreviousImage = (e) => {
    e.stopPropagation(); // Prevent modal from closing
    const prevIndex =
      (selectedImage.index - 1 + allImages.length) % allImages.length;
    setSelectedImage({ src: allImages[prevIndex].src, index: prevIndex });
  };

  // --- Pagination Handler ---
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0); // Scroll to top on page change
  };

  return (
    <>
      {/* --- Hero Section --- */}
      <div className="hero-section">
        <div className="hero-overlay"></div>
        <h1 className="hero-title">Life @ Makonis</h1>
      </div>

      <div className="gallery-container">
        <div className="header">
          <h2>Our Gallery</h2>
          <button className="memories-button" onClick={openMemoriesModal}>
            Play Memories Video 🎬
          </button>
        </div>

        {/* --- Image Grid --- */}
        <div className="image-grid">
          {paginatedImages.map((image, index) => (
            <div
              key={image.id}
              className="grid-item"
              onClick={() => openImageModal(image.src, index)}
            >
              <img src={image.src} alt={`Memory ${image.id}`} />
            </div>
          ))}
        </div>

        {/* --- Pagination Controls --- */}
        <div className="pagination">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={currentPage === page ? "active" : ""}
            >
              {page}
            </button>
          ))}
        </div>
      </div>

      {/* --- Full-screen Image Modal with Navigation --- */}
      {selectedImage && (
        <div className="modal-overlay" onClick={closeImageModal}>
          <span className="close-button" onClick={closeImageModal}>
            &times;
          </span>
          <button className="nav-arrow left-arrow" onClick={goToPreviousImage}>
            &#10094;
          </button>
          <img
            src={selectedImage.src}
            alt="Full screen view"
            className="modal-content"
          />
          <button className="nav-arrow right-arrow" onClick={goToNextImage}>
            &#10095;
          </button>
        </div>
      )}

      {/* --- Memories Slideshow Modal --- */}
      {showMemories && (
        <div className="modal-overlay" onClick={closeMemoriesModal}>
          <span className="close-button" onClick={closeMemoriesModal}>
            &times;
          </span>
          <div className="modal-content slideshow-content">
            <img
              key={currentMemoryIndex}
              src={allImages[currentMemoryIndex].src}
              alt="Memories slideshow"
            />
          </div>
        </div>
      )}

      {/* --- CSS Styles --- */}
      <style>{`
        :root {
          --primary-color: #007bff;
          --primary-hover: #0056b3;
        }
        body {
          margin: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
        
        /* --- Hero Section Styles --- */
        .hero-section {
          position: relative;
          height: 60vh;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: white;
          background-image: url('[https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80](https://images.unsplash.com/photo-1522071820081-009f0129c71c?ixlib=rb-4.0.3&auto=format&fit=crop&w=2070&q=80)');
          background-size: cover;
          background-position: center;
        }
        .hero-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.5);
        }
        .hero-title {
          font-size: 4rem;
          font-weight: bold;
          text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
          z-index: 1;
        }

        /* --- Gallery Container --- */
        .gallery-container {
          padding: 2rem 4rem;
        }
        .header {
          text-align: center;
          margin-bottom: 2.5rem;
        }
        .header h2 {
          font-size: 2.5rem;
          color: #333;
          margin-bottom: 1rem;
        }
        .memories-button {
          padding: 0.8rem 1.5rem;
          font-size: 1rem;
          background-color: var(--primary-color);
          color: white;
          border: none;
          border-radius: 5px;
          cursor: pointer;
          transition: background-color 0.3s;
        }
        .memories-button:hover {
          background-color: var(--primary-hover);
        }

        /* --- Image Grid Styles --- */
        .image-grid {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          gap: 1.5rem;
        }
        .grid-item {
          cursor: pointer;
          overflow: hidden;
          border-radius: 8px;
          box-shadow: 0 4px 8px rgba(0,0,0,0.1);
          aspect-ratio: 1 / 1;
        }
        .grid-item img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: transform 0.3s ease-in-out;
        }
        .grid-item:hover img {
          transform: scale(1.05);
        }

        /* --- Pagination Styles --- */
        .pagination {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 3rem;
        }
        .pagination button {
          margin: 0 0.3rem;
          padding: 0.5rem 1rem;
          border: 1px solid #ddd;
          background-color: white;
          cursor: pointer;
          transition: all 0.2s;
        }
        .pagination button:hover {
          background-color: #f0f0f0;
          border-color: #ccc;
        }
        .pagination button.active {
          background-color: var(--primary-color);
          color: white;
          border-color: var(--primary-color);
        }

        /* --- Modal Styles --- */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background-color: rgba(0, 0, 0, 0.9);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 1000;
        }
        .modal-content {
          max-width: 85vw;
          max-height: 85vh;
          object-fit: contain;
        }
        .slideshow-content img {
           animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
          from { opacity: 0.4; transform: scale(0.98); }
          to { opacity: 1; transform: scale(1); }
        }
        .close-button {
          position: absolute;
          top: 15px;
          right: 35px;
          color: #fff;
          font-size: 45px;
          font-weight: bold;
          cursor: pointer;
          transition: color 0.2s;
        }
        .close-button:hover {
            color: #ccc;
        }
        
        /* --- Modal Navigation Arrows --- */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.4);
            color: white;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            padding: 1rem;
            z-index: 1001;
            transition: background-color 0.2s;
        }
        .nav-arrow:hover {
            background-color: rgba(0, 0, 0, 0.7);
        }
        .left-arrow {
            left: 20px;
            border-radius: 0 5px 5px 0;
        }
        .right-arrow {
            right: 20px;
            border-radius: 5px 0 0 5px;
        }

        /* --- Responsive Adjustments --- */
        @media (max-width: 1024px) {
          .image-grid {
            grid-template-columns: repeat(3, 1fr);
          }
          .gallery-container {
            padding: 2rem;
          }
        }
        @media (max-width: 768px) {
          .hero-title {
            font-size: 3rem;
          }
          .image-grid {
            grid-template-columns: repeat(2, 1fr);
          }
          .gallery-container {
            padding: 1.5rem;
          }
        }
        @media (max-width: 480px) {
          .hero-title {
            font-size: 2.5rem;
          }
          .image-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </>
  );
};

export default GalleryPage;
