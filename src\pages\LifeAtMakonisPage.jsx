import React, { useState, useEffect, useRef } from "react";
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

// Register GSAP plugins
gsap.registerPlugin(ScrollTrigger);

// --- Image Imports ---
import img1 from "../assets/1.jpg";
import img3 from "../assets/3.jpg";
import img4 from "../assets/4.jpg";
import img5 from "../assets/5.jpg";
import img6 from "../assets/6.jpg";
import img7 from "../assets/7.jpg";
import img8 from "../assets/8.jpg";
import img9 from "../assets/9.jpg";
import img11 from "../assets/11.jpg";
import img13 from "../assets/13.jpg";
import img14 from "../assets/14.jpg";
import img15 from "../assets/15.jpg";
import img16 from "../assets/16.jpg";
import img18 from "../assets/18.jpg";
import img19 from "../assets/19.jpg";
import img20 from "../assets/20.jpg";
import img21 from "../assets/21.jpg";
import img22 from "../assets/22.jpg";
import img23 from "../assets/23.jpg";
import img25 from "../assets/25.jpg";
import img26 from "../assets/26.jpg";
import img28 from "../assets/28.jpg";
import img29 from "../assets/29.jpg";
import img30 from "../assets/30.jpeg";
import img31 from "../assets/31.jpeg";
import img32 from "../assets/32.jpeg";
import img33 from "../assets/33.png";
import img34 from "../assets/34.jpeg";

// Hero background image URL
const heroBackgroundUrl = "https://blog.ipleaders.in/wp-content/uploads/2018/11/BV-Acharya-27.jpg";

// Organize images into an array for easier mapping
const allImages = [
  { id: 1, src: img1 },
  { id: 3, src: img3 },
  { id: 4, src: img4 },
  { id: 5, src: img5 },
  { id: 6, src: img6 },
  { id: 7, src: img7 },
  { id: 8, src: img8 },
  { id: 9, src: img9 },
  { id: 11, src: img11 },
  { id: 13, src: img13 },
  { id: 14, src: img14 },
  { id: 15, src: img15 },
  { id: 16, src: img16 },
  { id: 18, src: img18 },
  { id: 19, src: img19 },
  { id: 20, src: img20 },
  { id: 21, src: img21 },
  { id: 22, src: img22 },
  { id: 23, src: img23 },
  { id: 25, src: img25 },
  { id: 26, src: img26 },
  { id: 28, src: img28 },
  { id: 29, src: img29 },
  { id: 30, src: img30 },
  { id: 31, src: img31 },
  { id: 32, src: img32 },
  { id: 33, src: img33 },
  { id: 34, src: img34 },
];

const IMAGES_PER_PAGE = 12;

const LifeAtMakonisPage = () => {
  // Refs for animations
  const pageWrapperRef = useRef(null);
  const heroRef = useRef(null);
  const heroTitleRef = useRef(null);
  const galleryRef = useRef(null);
  const backgroundRef = useRef(null);

  // State for the full-screen image modal (now stores index)
  const [selectedImage, setSelectedImage] = useState(null);

  // State for the "Memories" video/slideshow modal
  const [showMemories, setShowMemories] = useState(false);
  const [currentMemoryIndex, setCurrentMemoryIndex] = useState(0);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);

  // Calculate paginated images
  const totalPages = Math.ceil(allImages.length / IMAGES_PER_PAGE);
  const paginatedImages = allImages.slice(
    (currentPage - 1) * IMAGES_PER_PAGE,
    currentPage * IMAGES_PER_PAGE
  );

  // GSAP Animations
  useEffect(() => {
    const ctx = gsap.context(() => {
      // Hero animations
      const tl = gsap.timeline({ delay: 0.3 });

      if (heroTitleRef.current) {
        tl.from(heroTitleRef.current, {
          y: 50,
          opacity: 0,
          duration: 1.2,
          ease: "power3.out",
        });
      }

      // Gallery cards animation
      const galleryCards = document.querySelectorAll('.grid-item');
      galleryCards.forEach((card, index) => {
        gsap.from(card, {
          y: 50,
          opacity: 0,
          duration: 0.8,
          delay: index * 0.1,
          ease: "power2.out",
          scrollTrigger: {
            trigger: card,
            start: "top 85%",
            toggleActions: "play none none reverse",
          },
        });
      });

      // Background parallax effect
      if (backgroundRef.current) {
        gsap.to(backgroundRef.current, {
          yPercent: -50,
          ease: "none",
          scrollTrigger: {
            trigger: pageWrapperRef.current,
            start: "top bottom",
            end: "bottom top",
            scrub: true,
          },
        });
      }
    }, pageWrapperRef);

    return () => ctx.revert();
  }, [currentPage]); // Re-run when page changes to animate new cards

  // Cleanup effect to restore body scroll on unmount
  useEffect(() => {
    return () => {
      document.body.classList.remove('modal-open');
    };
  }, []);

  // Keyboard event handling for modals
  useEffect(() => {
    const handleKeyPress = (e) => {
      if (selectedImage) {
        if (e.key === 'Escape') {
          closeImageModal();
        } else if (e.key === 'ArrowLeft') {
          goToPreviousImage(e);
        } else if (e.key === 'ArrowRight') {
          goToNextImage(e);
        }
      }
      if (showMemories && e.key === 'Escape') {
        closeMemoriesModal();
      }
    };

    document.addEventListener('keydown', handleKeyPress);
    return () => document.removeEventListener('keydown', handleKeyPress);
  }, [selectedImage, showMemories]);

  // Effect for slideshow logic
  useEffect(() => {
    if (!showMemories) return;
    const slideshowInterval = setInterval(() => {
      setCurrentMemoryIndex((prevIndex) => (prevIndex + 1) % allImages.length);
    }, 2000);
    return () => clearInterval(slideshowInterval);
  }, [showMemories]);

  // --- Modal Handlers ---
  const openImageModal = (imageSrc) => {
    // We need the global index from `allImages` to navigate
    const globalIndex = allImages.findIndex((img) => img.src === imageSrc);
    setSelectedImage({ src: imageSrc, index: globalIndex });
    // Prevent body scroll
    document.body.classList.add('modal-open');
  };

  const closeImageModal = () => {
    setSelectedImage(null);
    // Restore body scroll
    document.body.classList.remove('modal-open');
  };

  const openMemoriesModal = () => {
    setCurrentMemoryIndex(0);
    setShowMemories(true);
    // Prevent body scroll
    document.body.classList.add('modal-open');
  };

  const closeMemoriesModal = () => {
    setShowMemories(false);
    // Restore body scroll
    document.body.classList.remove('modal-open');
  };

  // --- Navigation Handlers for Full-Screen Modal ---
  const goToNextImage = (e) => {
    e.stopPropagation(); // Prevent modal from closing
    const nextIndex = (selectedImage.index + 1) % allImages.length;
    setSelectedImage({ src: allImages[nextIndex].src, index: nextIndex });
  };

  const goToPreviousImage = (e) => {
    e.stopPropagation(); // Prevent modal from closing
    const prevIndex =
      (selectedImage.index - 1 + allImages.length) % allImages.length;
    setSelectedImage({ src: allImages[prevIndex].src, index: prevIndex });
  };

  // --- Pagination Handler ---
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
    window.scrollTo(0, 0); // Scroll to top on page change
  };

  return (
    <div
      ref={pageWrapperRef}
      className="life-at-makonis-wrapper relative overflow-hidden min-h-screen"
      style={{
        background: "linear-gradient(135deg, rgba(0, 43, 89, 0.95) 0%, rgba(0, 157, 230, 0.98) 100%)",
        backdropFilter: "blur(10px)",
      }}
    >
      {/* Background Grid Pattern */}
      <div ref={backgroundRef} className="absolute inset-0 z-0">
        <div
          className="absolute inset-0 opacity-20"
          style={{
            backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
            backgroundSize: "50px 50px",
          }}
        />
      </div>

      {/* --- Hero Section --- */}
      <div
        ref={heroRef}
        className="hero-section relative"
        style={{
          backgroundImage: `url(${heroBackgroundUrl})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundAttachment: "fixed",
        }}
      >
        <div className="hero-overlay"></div>
        <h1 ref={heroTitleRef} className="hero-title">Life @ Makonis</h1>
      </div>

      <div ref={galleryRef} className="gallery-container relative z-10">
        <div className="header">
          <h2>Our Gallery</h2>
          <button className="memories-button" onClick={openMemoriesModal}>
            Play Memories Video 🎬
          </button>
        </div>

        {/* --- Image Grid --- */}
        <div className="image-grid">
          {paginatedImages.map((image, index) => (
            <div
              key={image.id}
              className="grid-item"
              onClick={() => openImageModal(image.src)}
            >
              <img src={image.src} alt={`Memory ${image.id}`} />
            </div>
          ))}
        </div>

        {/* --- Pagination Controls --- */}
        <div className="pagination">
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={currentPage === page ? "active" : ""}
            >
              {page}
            </button>
          ))}
        </div>
      </div>

      {/* --- Full-screen Image Modal with Navigation --- */}
      {selectedImage && (
        <div className="modal-overlay" onClick={closeImageModal}>
          <span className="close-button" onClick={closeImageModal}>
            &times;
          </span>
          <button className="nav-arrow left-arrow" onClick={goToPreviousImage}>
            &#10094;
          </button>
          <img
            src={selectedImage.src}
            alt="Full screen view"
            className="modal-content"
          />
          <button className="nav-arrow right-arrow" onClick={goToNextImage}>
            &#10095;
          </button>
        </div>
      )}

      {/* --- Memories Slideshow Modal --- */}
      {showMemories && (
        <div className="modal-overlay" onClick={closeMemoriesModal}>
          <span className="close-button" onClick={closeMemoriesModal}>
            &times;
          </span>
          <div className="modal-content slideshow-content">
            <img
              key={currentMemoryIndex}
              src={allImages[currentMemoryIndex].src}
              alt="Memories slideshow"
            />
          </div>
        </div>
      )}

      {/* --- CSS Styles --- */}
      <style>{`
        :root {
          --makonis-primary: #002B59;
          --makonis-secondary: #009DE6;
          --makonis-gradient: linear-gradient(135deg, #002B59 0%, #009DE6 100%);
        }

        body {
          margin: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        /* Prevent body scroll when modal is open */
        body.modal-open {
          overflow: hidden;
        }

        /* Page wrapper styles */
        .life-at-makonis-wrapper {
          overflow-x: hidden;
        }

        /* --- Hero Section Styles --- */
        .hero-section {
          position: relative;
          height: 70vh;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          color: white;
          z-index: 2;
        }

        .hero-overlay {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, rgba(0, 43, 89, 0.7) 0%, rgba(0, 157, 230, 0.5) 100%);
          z-index: 1;
        }

        .hero-title {
          font-size: 4rem;
          font-weight: bold;
          text-shadow: 2px 2px 8px rgba(0,0,0,0.7);
          z-index: 2;
          position: relative;
          background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        /* --- Gallery Container --- */
        .gallery-container {
          padding: 3rem 2rem;
          background: rgba(255, 255, 255, 0.05);
          backdrop-filter: blur(15px);
          border-radius: 2rem 2rem 0 0;
          margin-top: -2rem;
          position: relative;
          z-index: 10;
        }

        .header {
          text-align: center;
          margin-bottom: 3rem;
        }

        .header h2 {
          font-size: 2.5rem;
          font-weight: bold;
          margin-bottom: 1rem;
          background: linear-gradient(135deg, #ffffff 0%, #00a0e9 100%);
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .memories-button {
          padding: 1rem 2rem;
          font-size: 1.1rem;
          background: var(--makonis-gradient);
          color: white;
          border: none;
          border-radius: 50px;
          cursor: pointer;
          transition: all 0.3s ease;
          box-shadow: 0 8px 32px rgba(0, 157, 230, 0.3);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .memories-button:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 40px rgba(0, 157, 230, 0.4);
        }

        /* --- Image Grid Styles --- */
        .image-grid {
          display: grid;
          grid-template-columns: repeat(3, 1fr);
          gap: 2rem;
          margin-bottom: 3rem;
        }

        .grid-item {
          cursor: pointer;
          overflow: hidden;
          border-radius: 20px;
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(15px);
          border: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.4s cubic-bezier(0.4, 2, 0.6, 1);
          box-shadow: 0 8px 32px rgba(0, 43, 89, 0.2);
          position: relative;
          height: 280px;
        }

        .grid-item::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(0, 157, 230, 0.1) 0%, rgba(0, 43, 89, 0.1) 100%);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 20px;
          z-index: 1;
        }

        .grid-item:hover::before {
          opacity: 1;
        }

        .grid-item img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          object-position: center;
          transition: transform 0.4s ease;
          border-radius: 20px;
        }

        .grid-item:hover {
          transform: translateY(-8px);
          box-shadow: 0 16px 48px rgba(0, 157, 230, 0.3);
        }

        .grid-item:hover img {
          transform: scale(1.05);
        }

        /* --- Pagination Styles --- */
        .pagination {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-top: 3rem;
          gap: 0.5rem;
        }

        .pagination button {
          padding: 0.8rem 1.2rem;
          border: 1px solid rgba(255, 255, 255, 0.2);
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          color: white;
          cursor: pointer;
          transition: all 0.3s ease;
          border-radius: 12px;
          font-weight: 500;
        }

        .pagination button:hover {
          background: rgba(0, 157, 230, 0.3);
          border-color: rgba(0, 157, 230, 0.5);
          transform: translateY(-2px);
        }

        .pagination button.active {
          background: var(--makonis-gradient);
          border-color: var(--makonis-secondary);
          box-shadow: 0 4px 16px rgba(0, 157, 230, 0.4);
        }

        /* --- Modal Styles --- */
        .modal-overlay {
          position: fixed;
          top: 0;
          left: 0;
          width: 100vw; /* Cover full viewport width */
          height: 100vh; /* Cover full viewport height */
          background-color: rgba(0, 0, 0, 0.9);
          display: flex;
          justify-content: center;
          align-items: center;
          z-index: 9999; /* High z-index to ensure it's on top of everything */
          overflow: hidden;
          animation: modalFadeIn 0.3s ease; /* Optional: Add a fade-in animation */
        }
        .modal-content {
          max-width: 85vw;
          max-height: 80vh; /* Slightly less to account for header space */
          object-fit: contain;
          object-position: center;
        }
        .slideshow-content {
          max-width: 85vw;
          max-height: 80vh;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        .slideshow-content img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
          object-position: center;
          animation: fadeIn 0.5s ease-in-out;
        }
        @keyframes fadeIn {
          from { opacity: 0.4; transform: scale(0.98); }
          to { opacity: 1; transform: scale(1); }
        }
        .close-button {
          position: absolute;
          top: 15px;
          right: 35px;
          color: #fff;
          font-size: 45px;
          font-weight: bold;
          cursor: pointer;
          transition: color 0.2s;
          z-index: 1002;
        }
        .close-button:hover {
            color: #ccc;
        }

        /* --- Modal Navigation Arrows --- */
        .nav-arrow {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background-color: rgba(0, 0, 0, 0.6);
            color: white;
            border: none;
            font-size: 2rem;
            cursor: pointer;
            padding: 1rem;
            z-index: 1002;
            transition: all 0.3s ease;
            border-radius: 8px;
        }
        .nav-arrow:hover {
            background-color: rgba(0, 157, 230, 0.8);
            transform: translateY(-50%) scale(1.1);
        }
        .left-arrow {
            left: 20px;
            border-radius: 0 5px 5px 0;
        }
        .right-arrow {
            right: 20px;
            border-radius: 5px 0 0 5px;
        }

        /* --- Responsive Adjustments --- */
        /* Desktop: 3 columns (default) */
        @media (max-width: 1200px) {
          .image-grid {
            grid-template-columns: repeat(3, 1fr);
            gap: 1.5rem;
          }
          .gallery-container {
            padding: 2.5rem 1.5rem;
          }
        }

        /* Tablet: 4 columns (as specified) */
        @media (max-width: 768px) {
          .hero-title {
            font-size: 3rem;
          }
          .image-grid {
            grid-template-columns: repeat(2, 1fr);
            gap: 1.5rem;
          }
          .gallery-container {
            padding: 2rem 1rem;
          }
          .grid-item {
            height: 220px;
          }
          .header h2 {
            font-size: 2rem;
          }
        }

        /* Mobile: 6 columns (1 column as specified) */
        @media (max-width: 480px) {
          .hero-title {
            font-size: 2.5rem;
          }
          .image-grid {
            grid-template-columns: 1fr;
            gap: 1rem;
          }
          .gallery-container {
            padding: 1.5rem 1rem;
          }
          .grid-item {
            height: 200px;
          }
          .header h2 {
            font-size: 1.8rem;
          }
          .memories-button {
            padding: 0.8rem 1.5rem;
            font-size: 1rem;
          }
        }
      `}</style>
    </div>
  );
};

export default LifeAtMakonisPage;
