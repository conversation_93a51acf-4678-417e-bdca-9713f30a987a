import React, { useRef, useState, useEffect } from 'react';
import { Container } from 'react-bootstrap';
import { FaArrowUp, FaMicrochip, FaShieldAlt } from 'react-icons/fa';
import PhysicalDesignPage from './PhysicalDesignPage';
import PhysicalVerificationPage from './PhysicalVerificationPage';

const Semiconductors = () => {
  const physicalDesignRef = useRef(null);
  const physicalVerificationRef = useRef(null);
  const [showScrollTop, setShowScrollTop] = useState(false);

  useEffect(() => {
    window.scrollTo(0, 0);

    const handleScroll = () => {
      setShowScrollTop(window.pageYOffset > 300);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToSection = (ref) => {
    ref.current?.scrollIntoView({
      behavior: 'smooth',
      block: 'start'
    });
  };

  const scrollToTop = () => {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <>
      {/* Hero Section */}
      <section
        className="hero-section position-relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, rgba(0, 41, 86, 0.9) 100%)`,
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center'
        }}
      >
        {/* Background Grid Pattern */}
        <div style={{ position: "absolute", inset: 0, zIndex: 0 }}>
          <div
            style={{
              position: "absolute",
              inset: 0,
              opacity: 0.2,
              backgroundImage: `linear-gradient(rgba(0, 160, 233, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(0, 160, 233, 0.1) 1px, transparent 1px)`,
              backgroundSize: "50px 50px",
            }}
          />
        </div>

        <Container className="position-relative" style={{ zIndex: 1 }}>
          <div className="text-center">
            <h1
              className="fw-bolder display-1 mb-4"
              style={{
                background: 'linear-gradient(135deg, #ffffff 0%, rgba(0, 160, 233, 1) 100%)',
                WebkitBackgroundClip: 'text',
                WebkitTextFillColor: 'transparent',
                textShadow: '0 0 40px rgba(0, 160, 233, 0.4)',
                fontSize: 'clamp(2.5rem, 6vw, 4.5rem)'
              }}
            >
              Semiconductor Solutions
            </h1>
            <p
              className="lead mb-5 mx-auto"
              style={{
                maxWidth: '1000px',
                color: '#c0c0c0',
                fontSize: 'clamp(1.1rem, 2.5vw, 1.6rem)'
              }}
            >
              From RTL to Silicon: Complete Physical Design and Verification Services for Advanced Semiconductor Nodes
            </p>

            {/* Navigation Buttons */}
            <div className="d-flex flex-column flex-md-row justify-content-center gap-3 mt-5">
              <button
                className="btn btn-lg px-4 py-3 d-flex align-items-center justify-content-center gap-3"
                onClick={() => scrollToSection(physicalDesignRef)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(15px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  borderRadius: '15px',
                  transition: 'all 0.3s ease',
                  fontSize: '1.1rem',
                  fontWeight: '600'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'rgba(0, 160, 233, 0.3)';
                  e.target.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.target.style.transform = 'translateY(0)';
                }}
              >
                <FaMicrochip size={20} />
                Physical Design
              </button>
              <button
                className="btn btn-lg px-4 py-3 d-flex align-items-center justify-content-center gap-3"
                onClick={() => scrollToSection(physicalVerificationRef)}
                style={{
                  background: 'rgba(255, 255, 255, 0.1)',
                  backdropFilter: 'blur(15px)',
                  border: '1px solid rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  borderRadius: '15px',
                  transition: 'all 0.3s ease',
                  fontSize: '1.1rem',
                  fontWeight: '600'
                }}
                onMouseEnter={(e) => {
                  e.target.style.background = 'rgba(0, 160, 233, 0.3)';
                  e.target.style.transform = 'translateY(-2px)';
                }}
                onMouseLeave={(e) => {
                  e.target.style.background = 'rgba(255, 255, 255, 0.1)';
                  e.target.style.transform = 'translateY(0)';
                }}
              >
                <FaShieldAlt size={20} />
                Physical Verification
              </button>
            </div>
          </div>
        </Container>
      </section>

      {/* Physical Design Section */}
      <div ref={physicalDesignRef} className="semiconductor-section">
        <PhysicalDesignPage />
      </div>

      {/* Physical Verification Section */}
      <div ref={physicalVerificationRef} className="semiconductor-section">
        <PhysicalVerificationPage />
      </div>

      {/* Scroll to Top Button */}
      <button
        className={`scroll-to-top ${showScrollTop ? 'visible' : ''}`}
        onClick={scrollToTop}
        aria-label="Scroll to top"
        style={{
          position: 'fixed',
          bottom: '30px',
          right: '30px',
          width: '50px',
          height: '50px',
          borderRadius: '50%',
          background: 'linear-gradient(135deg, #002B59 0%, #009DE6 100%)',
          border: 'none',
          color: 'white',
          fontSize: '20px',
          cursor: 'pointer',
          zIndex: 1000,
          opacity: showScrollTop ? 1 : 0,
          visibility: showScrollTop ? 'visible' : 'hidden',
          transition: 'all 0.3s ease',
          boxShadow: '0 4px 15px rgba(0, 160, 233, 0.3)'
        }}
        onMouseEnter={(e) => {
          e.target.style.transform = 'translateY(-3px)';
          e.target.style.boxShadow = '0 6px 20px rgba(0, 160, 233, 0.4)';
        }}
        onMouseLeave={(e) => {
          e.target.style.transform = 'translateY(0)';
          e.target.style.boxShadow = '0 4px 15px rgba(0, 160, 233, 0.3)';
        }}
      >
        <FaArrowUp />
      </button>

      <style jsx>{`
        .semiconductor-section {
          position: relative;
        }

        .scroll-to-top {
          transition: all 0.3s ease;
        }

        .scroll-to-top.visible {
          opacity: 1;
          visibility: visible;
        }

        .scroll-to-top:hover {
          transform: translateY(-3px);
        }
      `}</style>
    </>
  );
};

export default Semiconductors;